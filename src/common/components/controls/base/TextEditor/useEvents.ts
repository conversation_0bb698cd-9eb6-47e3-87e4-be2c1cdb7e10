import { useMemo } from 'react';
import useCurrentUser from '../../../../data/hooks/useCurrentUser';
import { IFroalaConfig } from './TextEditor';

const useEvents = ({
  fileCategory,
  events = {},
  setLoading, // eslint-disable-line @typescript-eslint/no-unused-vars
  onFileUpload, // eslint-disable-line @typescript-eslint/no-unused-vars
  setIsFocus,
  setCounter,
}: {
  fileCategory?: string;
  events?: IFroalaConfig['events'];
  setLoading?: (isLoading: boolean) => void;
  onFileUpload?: (fileId: string) => void;
  setIsFocus?: React.Dispatch<React.SetStateAction<boolean>>;
  setCounter: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const { me } = useCurrentUser();

  return useMemo(() => {
    const activeUploads = {};

    // Basic events that should always be available
    const basicEvents = {
      focus() {
        setIsFocus && setIsFocus(true);
      },
      blur() {
        setIsFocus && setIsFocus(false);
      },
      'charCounter.update'() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const count = (this as any).charCounter;
        setCounter(count?.count());
      },
      ...events,
    };

    // File upload events only when fileCategory and user are available
    if (!fileCategory || !me) {
      return basicEvents;
    }

    return {
      'edit.on'() {
        if (Object.keys(activeUploads).length > 0) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (this as any).edit.off();
        }
      },
      ...basicEvents,
    };
  }, [events, fileCategory, me, setIsFocus, setCounter]);
};

export default useEvents;
